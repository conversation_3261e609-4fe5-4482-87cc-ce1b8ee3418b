
"""
Replay actions from a parquet file using xrocs control API.

Usage Example:
    python replay_from_parquet.py /path/to/actions.parquet [config_path] [interval]
    # 例如：
    python replay_from_parquet.py ./data/actions.parquet /home/<USER>/Documents/configuration.toml 0.1
"""

import sys
from pathlib import Path
import time
import pandas as pd
import numpy as np
from xrocs.core.config_loader import ConfigLoader
from xrocs.core.station_loader import StationLoader
from xrocs.utils.logger.logger_loader import logger


def replay_from_parquet(parquet_path, config_path=None, interval=0.05):
    """
    Replay actions from a parquet file using xrocs control API.
    Args:
        parquet_path (str): Path to the parquet file containing actions.
        config_path (str): Path to the configuration file for xrocs.
        interval (float): Time interval (seconds) between actions.
    """
    if config_path is None:
        config_path = "/home/<USER>/Documents/configuration.toml"
    cfg_loader = ConfigLoader(config_path)
    cfg_dict = cfg_loader.get_config()
    station_loader = StationLoader(cfg_dict)
    robot_station = station_loader.generate_station_handle()
    robot_station.connect()

    logger.info(f"Loading actions from {parquet_path}")
    df = pd.read_parquet(parquet_path)
    logger.info(f"Loaded {len(df)} actions. Starting replay...")

    # 先查看DataFrame的结构
    logger.info(f"DataFrame columns: {df.columns}")
    logger.info(f"First row content: {df.iloc[0].to_dict()}")
    
    for idx, row in df.iterrows():
        action = row['action'] if 'action' in row else row.to_dict()
        # Debug: 打印action的类型和内容
        logger.info(f"Action type: {type(action)}")
        logger.info(f"Action content: {action}")
        
        # 如果是ndarray，尝试转换
        if isinstance(action, np.ndarray):
            logger.info(f"Action shape: {action.shape}")
            # 尝试调用decompose_action，与DataCollector中相同的处理方式
            action = robot_station.decompose_action(action)
            logger.info(f"After decompose - Action type: {type(action)}")
            logger.info(f"After decompose - Action content: {action}")
        
        robot_station.step(action)
        logger.info(f"Step {idx+1}/{len(df)} executed.")
        time.sleep(interval)
    logger.success("Replay finished.")
    robot_station.close()


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python replay_from_parquet.py <parquet_path> [config_path] [interval]")
        sys.exit(1)
    parquet_path = sys.argv[1]
    config_path = sys.argv[2] if len(sys.argv) > 2 else None
    interval = float(sys.argv[3]) if len(sys.argv) > 3 else 0.05
    replay_from_parquet(parquet_path, config_path, interval)
